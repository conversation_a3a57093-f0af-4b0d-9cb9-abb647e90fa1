import Config

# Note we also include the path to a cache manifest
# containing the digested version of static files. This
# manifest is generated by the `mix assets.deploy` task,
# which you should run after static files are built and
# before starting your production server.
config :reconciliation, ReconciliationWeb.Endpoint,
  cache_static_manifest: "priv/static/cache_manifest.json"



# Do not print debug messages in production
config :logger, level: :info

# Configure Bamboo mailer for production - ENABLED for external email sending
config :reconciliation, Reconciliation.Mailer,
  adapter: Bamboo.SMTPAdapter,
  server: "10.144.27.11",
  port: 25,
  username: "<EMAIL>",
  password: "<PERSON><PERSON>@123",
  tls: :if_available,
  no_mx_lookups: false,
  allowed_tls_versions: [:tlsv1, :"tlsv1.1", :"tlsv1.2"],
  ssl: false,
  auth: :always,
  retries: 2

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.

