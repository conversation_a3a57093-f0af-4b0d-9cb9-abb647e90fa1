# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :reconciliation,
  ecto_repos: [Reconciliation.Repo],
  generators: [timestamp_type: :utc_datetime]

# Configures the endpoint
config :reconciliation, ReconciliationWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: ReconciliationWeb.ErrorHTML, json: ReconciliationWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Reconciliation.PubSub,
  live_view: [signing_salt: "DKfOB8Gt"]



config :reconciliation,
  session_timeout: 3_600,
  smtp_username: "<EMAIL>",
  smtp_password: "Stanbic@123",
  smtp_server: "************",
  smtp_port: 25,
  smtp_tls: :if_available,
  smtp_no_mx_lookups: false,
  smtp_allowed_tls_versions: [:tlsv1, :"tlsv1.1", :"tlsv1.2"],
  smtp_ssl: false,
  smtp_auth: :never,

  }









# Configures the mailer
config :reconciliation, Reconciliation.Mailer,
  adapter: Bamboo.SMTPAdapter,
  server: "************",
  port: 25,
  username: "",
  password: "",
  tls: :if_available,
  no_mx_lookups: false,
  allowed_tls_versions: [:tlsv1, :"tlsv1.1", :"tlsv1.2"],
  ssl: false,
  auth: :never,
  retries: 2




# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  reconciliation: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.3",
  reconciliation: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]









# Configures Elixir's Logger
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]





# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
