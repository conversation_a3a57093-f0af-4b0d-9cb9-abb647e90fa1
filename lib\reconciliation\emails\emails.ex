
defmodule Reconciliation.Emails do
  @moduledoc """
  Email functions for the Reconciliation system.
  """
  
  import Bamboo.Email
  alias Reconciliation.Mailer

  @doc """
  Sends a welcome email to a new user with their temporary password.
  """
  def welcome_email(user, temporary_password, login_url) do
    new_email()
    |> from("<EMAIL>")
    |> to(user.email)
    |> subject("Welcome to ProBASE Reconciliation")
    |> html_body("""
    <h2>Welcome to ProBASE Reconciliation</h2>
    <p>Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},</p>
    <p>Your ProBASE Reconciliation account has been created. Please find your login credentials below:</p>
    <ul>
      <li><strong>Email:</strong> #{user.email}</li>
      <li><strong>Temporary Password:</strong> #{temporary_password}</li>
      <li><strong>Login URL:</strong> <a href="#{login_url}">#{login_url}</a></li>
    </ul>
    <p>Please log in and change your password immediately.</p>
    <p>Best regards,<br>ProBASE Reconciliation Team</p>
    """)
    |> text_body("""
    Welcome to ProBASE Reconciliation

    Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},

    Your ProBASE Reconciliation account has been created. Please find your login credentials below:

    Email: #{user.email}
    Temporary Password: #{temporary_password}
    Login URL: #{login_url}

    Please log in and change your password immediately.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Sends a password reset email with reset link.
  """
  def password_reset_email(user, reset_url) do
    new_email()
    |> from("<EMAIL>")
    |> to(user.email)
    |> subject("Reset your password - ProBASE Reconciliation")
    |> html_body("""
    <h2>Password Reset Request</h2>
    <p>Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},</p>
    <p>We received a password reset request for your ProBASE Reconciliation account (#{user.email}).</p>
    <p>Click the link below to reset your password:</p>
    <p><a href="#{reset_url}" style="background-color: #4ade80; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
    <p>If you didn't request this reset, please ignore this email.</p>
    <p>Best regards,<br>ProBASE Reconciliation Team</p>
    """)
    |> text_body("""
    Password Reset Request

    Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},

    We received a password reset request for your ProBASE Reconciliation account (#{user.email}).

    Click the link below to reset your password:
    #{reset_url}

    If you didn't request this reset, please ignore this email.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Sends a file upload confirmation email.
  """
  def file_upload_confirmation(user_email, filename, status) when status == "COMPLETE" do
    new_email()
    |> from("<EMAIL>")
    |> to(user_email)
    |> subject("File Upload Successful - ProBASE Reconciliation")
    |> text_body("""
    Dear User,

    Your file "#{filename}" has been uploaded successfully to the ProBASE Reconciliation system.

    You can now proceed with the reconciliation process.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  def file_upload_confirmation(user_email, filename, _status) do
    new_email()
    |> from("<EMAIL>")
    |> to(user_email)
    |> subject("File Upload Failed - ProBASE Reconciliation")
    |> text_body("""
    Dear User,

    Unfortunately, your file "#{filename}" could not be uploaded to the ProBASE Reconciliation system.

    Please try uploading the file again or contact support if the issue persists.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Sends a reconciliation completion alert.
  """
  def reconciliation_completion_alert(user_email, run_name, matched_count, unmatched_count) do
    new_email()
    |> from("<EMAIL>")
    |> to(user_email)
    |> subject("Reconciliation Complete - #{run_name}")
    |> html_body("""
    <h2>Reconciliation Complete</h2>
    <p>Dear User,</p>
    <p>Your reconciliation run "<strong>#{run_name}</strong>" has been completed successfully.</p>
    <h3>Results Summary:</h3>
    <ul>
      <li><strong>Matched Transactions:</strong> #{matched_count}</li>
      <li><strong>Unmatched Transactions:</strong> #{unmatched_count}</li>
    </ul>
    <p>You can now review the results and download the reconciliation report.</p>
    <p>Best regards,<br>ProBASE Reconciliation Team</p>
    """)
    |> text_body("""
    Reconciliation Complete

    Dear User,

    Your reconciliation run "#{run_name}" has been completed successfully.

    Results Summary:
    - Matched Transactions: #{matched_count}
    - Unmatched Transactions: #{unmatched_count}

    You can now review the results and download the reconciliation report.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Sends a reconciliation report via email with optional attachment.
  """
  def reconciliation_report_alert(user_email, run_name, nil) do
    new_email()
    |> from("<EMAIL>")
    |> to(user_email)
    |> subject("Reconciliation Report Ready - #{run_name}")
    |> text_body("""
    Dear User,

    The reconciliation report for "#{run_name}" has been generated and is ready for download.

    Please log in to the ProBASE Reconciliation system to access your report.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  def reconciliation_report_alert(user_email, run_name, attachment_path) do
    new_email()
    |> from("<EMAIL>")
    |> to(user_email)
    |> subject("Reconciliation Report - #{run_name}")
    |> text_body("""
    Dear User,

    Please find attached the reconciliation report for "#{run_name}".

    Best regards,
    ProBASE Reconciliation Team
    """)
    |> put_attachment(attachment_path)
  end

  @doc """
  Delivers an email using the configured mailer.
  """
  def deliver(email) do
    try do
      result = Mailer.deliver_now(email)
      {:ok, result}
    rescue
      error ->
        {:error, error}
    end
  end
end


